package com.bestpay.bigdata.bi.database.mapper.datascreen;

import com.bestpay.bigdata.bi.database.dao.datascreen.IndexTextComponentDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.ReportComponentDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-28
 */
public interface IndexComponentMapper {
    Long insert(IndexTextComponentDO indexTextComponentDO);

    Long insertShade(IndexTextComponentDO indexTextComponentDO);

    IndexTextComponentDO findById(Long id);

    List<IndexTextComponentDO> findAll();

    Long update(IndexTextComponentDO IndexTextComponentDO);

    List<IndexTextComponentDO> queryByIdListAndDataSetId(@Param("dataScreenIndexCardIdList") List<Long> dataScreenIndexCardIdList, @Param("datasetId") Long datasetId);
}
